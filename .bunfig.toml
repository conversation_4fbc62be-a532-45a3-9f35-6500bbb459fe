# Bun configuration for React Native/Expo project

[install]
# Use exact versions for better reproducibility
exact = true

# Cache directory (optional, <PERSON><PERSON> will use default if not specified)
# cache = "~/.bun/install/cache"

# Registry configuration
registry = "https://registry.npmjs.org"

# Peer dependency handling
peer = true

# Production dependencies only (when NODE_ENV=production)
production = false

# Optional dependencies
optional = true

# Development dependencies
dev = true

[install.scopes]
# Configure scoped registries if needed
# "@mycompany" = "https://npm.mycompany.com"

[run]
# Shell to use for running scripts
shell = "bash"

# Environment variables for scripts
# [run.env]
# NODE_ENV = "development"

[test]
# Test runner configuration (if using <PERSON><PERSON>'s built-in test runner)
preload = []

[build]
# Build configuration
target = "node"
