{"permissions": {"allow": ["<PERSON><PERSON>(python3:*)", "<PERSON><PERSON>(tmux new-session:*)", "<PERSON><PERSON>(cat:*)", "<PERSON><PERSON>(tmux list-sessions:*)", "<PERSON><PERSON>(tmux capture-pane:*)", "<PERSON><PERSON>(curl:*)", "Bash(ping:*)", "Bash(gcloud projects list:*)", "Bash(gcloud config list:*)", "Bash(gcloud config set:*)", "Bash(export CLOUDSDK_CORE_PROJECT=alias)", "Bash(gcloud iap oauth-brands create:*)", "Bash(gcloud alpha iap oauth-brands list:*)", "Bash(gcloud iap oauth-brands list:*)"], "deny": []}}