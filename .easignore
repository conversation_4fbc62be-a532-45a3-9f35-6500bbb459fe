# EAS Build ignore file
# Similar to .gitignore but for EAS builds
# https://docs.expo.dev/build-reference/easignore/

# Scripts directory - development and setup scripts not needed in builds
scripts/

# Common development files that shouldn't be included in builds
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Development environment files
.env.local
.env.development
.env.test

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary files
*.tmp
*.temp

# Documentation and markdown files (optional - remove if needed in build)
*.md
docs/

# Test files (optional - uncomment if you don't want tests in builds)
# __tests__/
# **/*.test.*
# **/*.spec.* 