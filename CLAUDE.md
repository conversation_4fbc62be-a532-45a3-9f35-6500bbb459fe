# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

ExpoBase is a production-ready React Native starter template built with Expo SDK 53. It provides comprehensive features including authentication, premium subscriptions, AI chat, task management, and internationalization.

**Package Manager**: This project uses **Bun** for faster dependency management and script execution. Bun is significantly faster than npm/yarn for installing packages and running scripts.

## Development Commands

### Core Development
- `bun install` - Install dependencies (faster than npm)
- `bun start` or `bun dev` - Start Expo development server
- `bun run ios` - Run on iOS simulator
- `bun run android` - Run on Android emulator
- `bun run web` - Run in web browser

### Code Quality
- `bun run lint` - Run ESLint and Prettier checks
- `bun run format` - Fix ESLint issues and format code with Prettier
- `bun run type-check` - Run TypeScript type checking

### Project Setup
- `bun run init` - Interactive setup for new projects (configures app name, bundle ID, URLs)
- `bun run setup:env` - Setup environment variables
- `bun run supabase:setup` - Initialize Supabase backend
- `bun run install:clean` - Clean install (removes node_modules and lockfile)

### Build & Deploy
- `bun run prebuild` - Generate native projects
- `bunx eas build` - Build with EAS (requires EAS CLI)

## Architecture & Code Patterns

### Navigation (Expo Router v5)
- File-based routing in `/app` directory
- Protected routes in `app/(protected)/`
- Authentication screens in `app/(auth)/`
- Tab navigation with drawer in `app/(protected)/(tabs)/`

### State Management
- **Zustand** for client state in `/stores`
- **React Query** for server state and API calls
- **MMKV** for persistent storage
- Always use optimistic updates for better UX

### Component Structure
- Base UI components in `/components/ui/` using NativeWind
- Feature-specific components organized by domain
- All components use TypeScript with proper types
- Follow existing component patterns for consistency

### API Integration
- Supabase client configured in `/config/supabase.ts`
- Custom hooks in `/hooks` for data fetching
- Edge functions in `/supabase/functions/`
- Always handle loading and error states

### Styling
- Use NativeWind (TailwindCSS) classes
- Follow existing theme patterns in `theme/colors.ts`
- Support both light and dark modes
- Use `cn()` utility for conditional classes

### Authentication Flow
1. Auth state managed in `AuthContext`
2. Supabase handles authentication
3. Profile automatically created on first sign-in
4. Session persisted with MMKV

### TypeScript Requirements
- Strict mode enabled
- Use path aliases: `@/` for root imports
- Define proper types for all props and state
- Avoid `any` types

### Form Handling
- Use React Hook Form with Zod validation
- Follow existing form patterns in auth components
- Show proper error messages with Toast

### Error Handling
- Use try-catch blocks for async operations
- Show user-friendly error messages with Toast
- Log errors to Sentry in production
- Handle network connectivity issues

### Testing Approach
Currently no test setup. When adding tests:
- Use Jest with React Native Testing Library
- Test critical user flows
- Mock external dependencies

## Key Files & Directories

### Configuration
- `app.json` - Expo configuration
- `tsconfig.json` - TypeScript configuration
- `.env` - Environment variables (create from `.env.template`)
- `eas.json` - EAS Build configuration

### Core Features
- `/app/_layout.tsx` - Root layout with providers
- `/context/AuthContext.tsx` - Authentication logic
- `/stores/profileStore.ts` - User profile state
- `/hooks/useTasks.ts` - Task management logic
- `/components/ai/ChatView.tsx` - AI chat implementation

### Backend
- `/supabase/migrations/` - Database schema
- `/supabase/functions/` - Edge functions
- Database tables: users, onboarding, tasks, payments

## Important Patterns

### Protected Routes
```typescript
// Always check authentication in protected routes
if (!isAuthenticated) {
  return <Redirect href="/sign-in" />;
}
```

### API Calls
```typescript
// Use React Query for data fetching
const { data, isLoading, error } = useQuery({
  queryKey: ['tasks'],
  queryFn: fetchTasks,
});
```

### Toast Messages
```typescript
// Show feedback to users
Toast.show({
  type: 'success',
  text1: 'Success',
  text2: 'Operation completed',
});
```

### Form Validation
```typescript
// Use Zod schemas with React Hook Form
const schema = z.object({
  email: z.string().email(),
  password: z.string().min(6),
});
```

## Environment Setup

Required environment variables:
- `EXPO_PUBLIC_SUPABASE_URL` - Supabase project URL
- `EXPO_PUBLIC_SUPABASE_ANON_KEY` - Supabase anonymous key
- `EXPO_PUBLIC_OPENAI_API_KEY` - OpenAI API key (for AI features)
- `EXPO_PUBLIC_STRIPE_PUBLISHABLE_KEY` - Stripe public key
- `EXPO_PUBLIC_REVENUECAT_PUBLIC_KEY` - RevenueCat key

## Common Tasks

### Adding a New Screen
1. Create file in appropriate `/app` directory
2. Use consistent layout pattern
3. Handle loading/error states
4. Add to navigation if needed

### Adding API Endpoint
1. Create edge function in `/supabase/functions/`
2. Add types to match database schema
3. Create custom hook in `/hooks/`
4. Handle errors appropriately

### Updating Translations
1. Edit files in `/translations/` directory
2. Follow existing key naming patterns
3. Test with language switcher

### Adding Premium Feature
1. Check premium status with `usePremiumStatus()`
2. Show `<PremiumModal>` for non-premium users
3. Update RevenueCat products if needed