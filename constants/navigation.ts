import { NavigationItem } from '@/types/navigation.d';

export const navigationItems: NavigationItem[] = [
  {
    key: 'home',
    labelKey: 'navigation.home.label',
    titleKey: 'navigation.home.title',
    subtitleKey: 'navigation.home.subtitle',
    icon: 'Home',
    route: '/(protected)/(tabs)/',
  },
  {
    key: 'tasks',
    labelKey: 'navigation.tasks.label',
    titleKey: 'navigation.tasks.title',
    subtitleKey: 'navigation.tasks.subtitle',
    icon: 'CheckSquare',
    route: '/(protected)/(tabs)/tasks',
  },
  {
    key: 'premium',
    labelKey: 'navigation.premium.label',
    titleKey: 'navigation.premium.title',
    subtitleKey: 'navigation.premium.subtitle',
    icon: 'Shield',
    route: '/(protected)/(tabs)/premium',
    premiumOnly: true,
  },
  {
    key: 'payment',
    labelKey: 'navigation.payment.label',
    titleKey: 'navigation.payment.title',
    subtitleKey: 'navigation.payment.subtitle',
    icon: 'CreditCard',
    route: '/(protected)/(tabs)/payment',
  },
  {
    key: 'ai',
    labelKey: 'navigation.ai.label',
    titleKey: 'navigation.ai.title',
    subtitleKey: 'navigation.ai.subtitle',
    icon: 'Bot',
    route: '/(protected)/(tabs)/ai',
  },
  {
    key: 'profile',
    labelKey: 'navigation.profile.label',
    titleKey: 'navigation.profile.title',
    subtitleKey: 'navigation.profile.subtitle',
    icon: 'User',
    route: '/(protected)/(tabs)/profile',
  },
  {
    key: 'settings',
    labelKey: 'navigation.settings.label',
    titleKey: 'navigation.settings.title',
    subtitleKey: 'navigation.settings.subtitle',
    icon: 'Settings',
    route: '/(protected)/(tabs)/settings',
  },
  {
    key: 'offline',
    labelKey: 'navigation.offline.label',
    titleKey: 'navigation.offline.title',
    subtitleKey: 'navigation.offline.subtitle',
    icon: 'LogOut',
    route: '/(protected)/(tabs)/offline',
  },
  {
    key: 'notifications',
    labelKey: 'navigation.notifications.label',
    titleKey: 'navigation.notifications.title',
    subtitleKey: 'navigation.notifications.subtitle',
    icon: 'Bell',
    route: '/(protected)/(tabs)/notifications',
  },
];
