@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
	* {
		box-sizing: border-box;
	}
	
	:root {
		/* Default variables - Light theme */
		--background: 0 0% 100%;
		--foreground: 20 14.3% 4.1%;
		--card: 0 0% 100%;
		--card-foreground: 20 14.3% 4.1%;
		--popover: 0 0% 100%;
		--popover-foreground: 20 14.3% 4.1%;
		--primary: 24.6 95% 53.1%;
		--primary-foreground: 60 9.1% 97.8%;
		--secondary: 60 4.8% 95.9%;
		--secondary-foreground: 25 5.3% 44.7%;
		--muted: 60 4.8% 95.9%;
		--muted-foreground: 25 5.3% 44.7%;
		--accent: 60 4.8% 95.9%;
		--accent-foreground: 24 9.8% 10%;
		--destructive: 0 84.2% 60.2%;
		--destructive-foreground: 60 9.1% 97.8%;
		--border: 20 5.9% 90%;
		--input: 20 5.9% 90%;
		--ring: 24.6 95% 53.1%;
		--radius: 0.65rem;
		--chart-1: 12 76% 61%;
		--chart-2: 173 58% 39%;
		--chart-3: 197 37% 24%;
		--chart-4: 43 74% 66%;
		--chart-5: 27 87% 67%;
	}

	.dark {
		/* Variables for Dark theme */
		--background: 20 14.3% 4.1%;
		--foreground: 60 9.1% 97.8%;
		--card: 20 14.3% 4.1%;
		--card-foreground: 60 9.1% 97.8%;
		--popover: 20 14.3% 4.1%;
		--popover-foreground: 60 9.1% 97.8%;
		--primary: 20.5 90.2% 48.2%;
		--primary-foreground: 60 9.1% 97.8%;
		--secondary: 12 6.5% 15.1%;
		--secondary-foreground: 60 9.1% 97.8%;
		--muted: 12 6.5% 15.1%;
		--muted-foreground: 24 5.4% 63.9%;
		--accent: 12 6.5% 15.1%;
		--accent-foreground: 60 9.1% 97.8%;
		--destructive: 0 72.2% 50.6%;
		--destructive-foreground: 60 9.1% 97.8%;
		--border: 12 6.5% 15.1%;
		--input: 12 6.5% 15.1%;
		--ring: 20.5 90.2% 48.2%;
	}
}

/* Classes utilitaires pour React Native avec NativeWind */
.bg-background { background-color: hsl(var(--background)); }
.bg-foreground { background-color: hsl(var(--foreground)); }
.bg-card { background-color: hsl(var(--card)); }
.bg-card-foreground { background-color: hsl(var(--card-foreground)); }
.bg-primary { background-color: hsl(var(--primary)); }
.bg-primary-foreground { background-color: hsl(var(--primary-foreground)); }
.bg-secondary { background-color: hsl(var(--secondary)); }
.bg-secondary-foreground { background-color: hsl(var(--secondary-foreground)); }
.bg-muted { background-color: hsl(var(--muted)); }
.bg-muted-foreground { background-color: hsl(var(--muted-foreground)); }
.bg-accent { background-color: hsl(var(--accent)); }
.bg-accent-foreground { background-color: hsl(var(--accent-foreground)); }
.bg-destructive { background-color: hsl(var(--destructive)); }
.bg-destructive-foreground { background-color: hsl(var(--destructive-foreground)); }

.text-background { color: hsl(var(--background)); }
.text-foreground { color: hsl(var(--foreground)); }
.text-card { color: hsl(var(--card)); }
.text-card-foreground { color: hsl(var(--card-foreground)); }
.text-primary { color: hsl(var(--primary)); }
.text-primary-foreground { color: hsl(var(--primary-foreground)); }
.text-secondary { color: hsl(var(--secondary)); }
.text-secondary-foreground { color: hsl(var(--secondary-foreground)); }
.text-muted { color: hsl(var(--muted)); }
.text-muted-foreground { color: hsl(var(--muted-foreground)); }
.text-accent { color: hsl(var(--accent)); }
.text-accent-foreground { color: hsl(var(--accent-foreground)); }
.text-destructive { color: hsl(var(--destructive)); }
.text-destructive-foreground { color: hsl(var(--destructive-foreground)); }

.border-background { border-color: hsl(var(--background)); }
.border-foreground { border-color: hsl(var(--foreground)); }
.border-card { border-color: hsl(var(--card)); }
.border-card-foreground { border-color: hsl(var(--card-foreground)); }
.border-primary { border-color: hsl(var(--primary)); }
.border-primary-foreground { border-color: hsl(var(--primary-foreground)); }
.border-secondary { border-color: hsl(var(--secondary)); }
.border-secondary-foreground { border-color: hsl(var(--secondary-foreground)); }
.border-muted { border-color: hsl(var(--muted)); }
.border-muted-foreground { border-color: hsl(var(--muted-foreground)); }
.border-accent { border-color: hsl(var(--accent)); }
.border-accent-foreground { border-color: hsl(var(--accent-foreground)); }
.border-destructive { border-color: hsl(var(--destructive)); }
.border-destructive-foreground { border-color: hsl(var(--destructive-foreground)); }
.border-border { border-color: hsl(var(--border)); }
.border-input { border-color: hsl(var(--input)); }
.border-ring { border-color: hsl(var(--ring)); }
  