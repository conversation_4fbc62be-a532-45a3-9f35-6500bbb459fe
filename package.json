{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "1.0.0", "platforms": ["ios", "android"], "scripts": {"android": "expo run:android", "ios": "expo run:ios", "start": "expo start", "prebuild": "expo prebuild", "lint": "eslint \"**/*.{js,jsx,ts,tsx}\" && prettier -c \"**/*.{js,jsx,ts,tsx,json}\"", "format": "eslint \"**/*.{js,jsx,ts,tsx}\" --fix && prettier \"**/*.{js,jsx,ts,tsx,json}\" --write", "web": "expo start --web", "concat": "node concat-code.js", "supabase:setup": "node scripts/supabase-setup.js", "setup:env": "node scripts/setup-env.js", "init": "node scripts/init.js"}, "dependencies": {"@expo/metro-runtime": "~5.0.4", "@hookform/resolvers": "^5.0.1", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-community/netinfo": "^11.4.1", "@react-native-google-signin/google-signin": "^14.0.1", "@react-navigation/drawer": "^7.3.9", "@rn-primitives/avatar": "^1.2.0", "@rn-primitives/checkbox": "^1.2.0", "@rn-primitives/dialog": "^1.2.0", "@rn-primitives/label": "^1.2.0", "@rn-primitives/select": "^1.2.0", "@rn-primitives/slot": "^1.2.0", "@rn-primitives/table": "^1.2.0", "@rn-primitives/tabs": "^1.2.0", "@sentry/react-native": "~6.14.0", "@stardazed/streams-text-encoding": "^1.0.2", "@stripe/stripe-react-native": "0.45.0", "@supabase/supabase-js": "^2.49.8", "@tanstack/react-query": "^5.76.2", "@ungap/structured-clone": "^1.3.0", "base64-arraybuffer": "^1.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "expo": "53.0.18", "expo-apple-authentication": "~7.2.4", "expo-blur": "~14.1.5", "expo-clipboard": "~7.1.5", "expo-constants": "~17.1.6", "expo-dev-client": "~5.2.4", "expo-device": "~7.1.4", "expo-doctor": "^1.13.5", "expo-haptics": "~14.1.4", "expo-image-picker": "~16.1.4", "expo-linking": "~7.1.7", "expo-localization": "~16.1.6", "expo-notifications": "~0.31.4", "expo-router": "~5.1.3", "expo-splash-screen": "~0.30.10", "expo-status-bar": "~2.2.3", "expo-system-ui": "~5.0.10", "expo-updates": "~0.28.16", "i18next": "^25.2.0", "lucide-react-native": "^0.511.0", "nativewind": "latest", "react": "19.0.0", "react-hook-form": "^7.57.0", "react-i18next": "^15.5.2", "react-native": "0.79.5", "react-native-gesture-handler": "~2.24.0", "react-native-mmkv": "^3.2.0", "react-native-purchases": "^8.10.1", "react-native-purchases-ui": "^8.10.1", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-svg": "15.11.2", "react-native-toast-message": "^2.3.0", "react-native-url-polyfill": "^2.0.0", "react-native-web": "^0.20.0", "sonner-native": "^0.20.0", "tailwind-merge": "^3.3.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.25.55", "zustand": "^5.0.5"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "~19.0.10", "dotenv": "^16.5.0", "eslint": "^9.25.1", "eslint-config-expo": "^9.2.0", "eslint-config-prettier": "^10.1.2", "node-fetch": "^3.3.2", "prettier": "^3.2.5", "prettier-plugin-tailwindcss": "^0.5.11", "supabase": "beta", "tailwindcss": "^3.4.0", "typescript": "~5.8.3"}, "main": "expo-router/entry", "private": true}